<?php

declare(strict_types=1);

namespace App\Tests\Services;

use App\Services\GermanyHolidayResolver;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Contracts\Translation\TranslatorInterface;

class GermanyHolidayResolverTest extends TestCase
{
    private GermanyHolidayResolver $germanyHolidayResolver;
    private MockObject $translator;

    public function setUp(): void
    {
        $this->translator = $this->createMock(originalClassName: TranslatorInterface::class);
        $this->translator->method('trans')->willReturnArgument(0);
        $this->germanyHolidayResolver = new GermanyHolidayResolver(translator: $this->translator);
    }

    public function testIsPublicHoliday(): void
    {
        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2022-01-01', states: ['NW']);
        $this->assertTrue($holiday['boolean']);
        $this->assertEquals('holiday.new_year', $holiday['name']);
    }

    public function testIsNotPublicHoliday(): void
    {
        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2022-01-02', states: ['NW']);
        $this->assertFalse($holiday['boolean']);
    }

    public function testBussUndBettag(): void
    {
        // In 2022, Buß- und Bettag was on November 16th.
        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2022-11-16', states: ['SN']);
        $this->assertTrue($holiday['boolean']);
        $this->assertEquals('holiday.repentance_day', $holiday['name']);

        // A day before is not a holiday
        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2022-11-15', states: ['SN']);
        $this->assertFalse($holiday['boolean']);

        // A day after is not a holiday
        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2022-11-17', states: ['SN']);
        $this->assertFalse($holiday['boolean']);

        // In another state it is not a holiday
        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2022-11-16', states: ['NW']);
        $this->assertFalse($holiday['boolean']);

        // In 2023, Buß- und Bettag was on November 22nd.
        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2023-11-22', states: ['SN']);
        $this->assertTrue($holiday['boolean']);
        $this->assertEquals('holiday.repentance_day', $holiday['name']);
    }

    public function testStateSpecificHoliday(): void
    {
        // Heilige Drei Könige is a holiday in BW, BY, ST
        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2022-01-06', states: ['BW']);
        $this->assertTrue($holiday['boolean']);

        $holiday = $this->germanyHolidayResolver->resolveHoliday(date: '2022-01-06', states: ['NW']);
        $this->assertFalse($holiday['boolean']);
    }
}

<?php

declare(strict_types=1);

namespace App\Tests\Services;

use App\Services\HolidayResolverInterface;
use App\Services\PublicHolidayHelper;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class PublicHolidayHelperTest extends TestCase
{
    private PublicHolidayHelper $publicHolidayHelper;
    private MockObject $germanyResolver;

    public function setUp(): void
    {
        $this->germanyResolver = $this->createMock(originalClassName: HolidayResolverInterface::class);

        $this->publicHolidayHelper = new PublicHolidayHelper(resolvers: [
            $this->germanyResolver,
        ]);
    }

    public function testFindAllPublicHolidaysForGermany(): void
    {
        $startDate = new \DateTime(datetime: '2024-12-24');
        $endDate = new \DateTime(datetime: '2024-12-26');
        $states = ['NW'];
        $countryCode = 'DE';

        $this->germanyResolver->method('supports')->with($countryCode)->willReturn(true);

        $invocation = 0;
        $this->germanyResolver->expects($this->exactly(3))
            ->method('resolveHoliday')
            ->willReturnCallback(function (string $date, array $receivedStates) use (&$invocation, $states): array|false {
                ++$invocation;
                switch ($invocation) {
                    case 1:
                        $this->assertEquals('2024-12-24', $date);
                        $this->assertEquals($states, $receivedStates);

                        return ['boolean' => true, 'name' => 'Christmas Eve'];
                    case 2:
                        $this->assertEquals('2024-12-25', $date);
                        $this->assertEquals($states, $receivedStates);

                        return ['boolean' => true, 'name' => 'Christmas Day'];
                    case 3:
                        $this->assertEquals('2024-12-26', $date);
                        $this->assertEquals($states, $receivedStates);

                        return ['boolean' => false]; // Not a holiday in NW
                }

                return false; // Should not be reached
            });

        $holidays = $this->publicHolidayHelper->findAllPublicHolidayInBetween(countryCode: $countryCode, startDate: $startDate, endDate: $endDate, states: $states);

        $this->assertCount(2, $holidays);
        $this->assertEquals(new \DateTime(datetime: '2024-12-24'), $holidays[0]['date']);
        $this->assertEquals('Christmas Eve', $holidays[0]['name']);
    }

    public function testCheckHolidayDayForGermany(): void
    {
        $date = '2024-01-01';
        $states = ['BY'];
        $countryCode = 'DE';

        $this->germanyResolver->method('supports')->with($countryCode)->willReturn(true);

        $this->germanyResolver->expects($this->once())
            ->method('resolveHoliday')
            ->with($date, $states)
            ->willReturn(['boolean' => true, 'name' => 'New Year']);

        $result = $this->publicHolidayHelper->checkHolidayDay(countryCode: $countryCode, date: $date, states: $states);

        $this->assertEquals(['boolean' => true, 'name' => 'New Year'], $result);
    }

    public function testThrowsExceptionForUnsupportedCountry(): void
    {
        $countryCode = 'LL'; // LL is not supported

        $this->germanyResolver->method('supports')->with($countryCode)->willReturn(false);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("No holiday resolver found for country 'LL'");

        $this->publicHolidayHelper->findAllPublicHolidayInBetween(
            countryCode: $countryCode,
            startDate: new \DateTime(),
            endDate: new \DateTime(),
            states: []
        );
    }
}

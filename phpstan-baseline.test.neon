parameters:
	ignoreErrors:
		-
			message: '#^Cannot access offset ''boolean'' on array\|false\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 9
			path: tests/Services/GermanyHolidayResolverTest.php

		-
			message: '#^Missing parameter \$type \(class\-string\<object\>\) in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)\.$#'
			identifier: argument.missing
			count: 1
			path: tests/Services/GermanyHolidayResolverTest.php

		-
			message: '#^Parameter \$translator of class App\\Services\\GermanyHolidayResolver constructor expects Symfony\\Contracts\\Translation\\TranslatorInterface, PHPUnit\\Framework\\MockObject\\MockObject given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Services/GermanyHolidayResolverTest.php

		-
			message: '#^Unable to resolve the template type RealInstanceType in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)$#'
			identifier: argument.templateType
			count: 1
			path: tests/Services/GermanyHolidayResolverTest.php

		-
			message: '#^Unknown parameter \$originalClassName in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)\.$#'
			identifier: argument.unknown
			count: 1
			path: tests/Services/GermanyHolidayResolverTest.php

		-
			message: '#^Cannot access offset ''date'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 1
			path: tests/Services/PublicHolidayHelperTest.php

		-
			message: '#^Cannot access offset ''name'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 1
			path: tests/Services/PublicHolidayHelperTest.php

		-
			message: '#^Missing parameter \$type \(class\-string\<object\>\) in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)\.$#'
			identifier: argument.missing
			count: 1
			path: tests/Services/PublicHolidayHelperTest.php

		-
			message: '#^Unable to resolve the template type RealInstanceType in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)$#'
			identifier: argument.templateType
			count: 1
			path: tests/Services/PublicHolidayHelperTest.php

		-
			message: '#^Unknown parameter \$originalClassName in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)\.$#'
			identifier: argument.unknown
			count: 1
			path: tests/Services/PublicHolidayHelperTest.php
